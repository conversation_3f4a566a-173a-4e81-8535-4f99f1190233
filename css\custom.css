/* Custom CSS to override the blue gradient and allow background images to show */
.hero-wrap {
  background-image: url('../images/Main_Bg.jpg') !important; /* Force the Main_Bg image */
  background-color: transparent !important; /* Remove background color */
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  position: relative;
  overflow: visible; /* Changed from hidden to visible to prevent content cutoff */
}

/* Special background for home page */
body.home-page .hero-wrap {
  background-image: url('../images/Home_Bg.jpg') !important;
}

.hero-wrap::before {
  display: none !important; /* Remove the radial gradients */
}

.hero-wrap .overlay {
  background: rgba(0, 0, 0, 0.3) !important; /* Add a semi-transparent dark overlay */
  opacity: 1 !important;
  background-image: none !important;
  background-color: transparent !important;
}

.hero-wrap.hero-wrap-2 {
  background-image: url('../images/Main_Bg.jpg') !important;
  background-color: transparent !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
}

.hero-wrap.hero-wrap-2 .overlay {
  background: rgba(0, 0, 0, 0.3) !important;
  opacity: 1 !important;
  background-image: none !important;
  background-color: transparent !important;
}

.contact-hero {
  background-image: url('../images/Main_Bg.jpg') !important;
  background-color: transparent !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
}

/* Accessibility Improvements */
a:focus, button:focus, input:focus, textarea:focus {
  outline: 2px solid #3e64ff;
}

/* Stacked heading styling */
.hero-wrap h1 span {
  display: inline-block;
  margin-bottom: 5px;
}

.hero-wrap h1 {
  line-height: 1.4;
  font-size: 42px !important;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 30px;
}

/* Improve responsive behavior */
@media (max-width: 768px) {
  .hero-wrap {
    height: auto;
    min-height: 100vh;
    padding-bottom: 40px;
  }

  /* Add margin to the blue section to prevent overlap with hero content */
  .ftco-section.ftco-no-pb.ftco-no-pt.bg-primary {
    margin-top: 20px;
  }

  /* Adjust text size in hero section for better mobile display */
  .hero-wrap h1 {
    font-size: 32px !important;
  }

  .hero-wrap p {
    font-size: 16px !important;
  }

  .hero-wrap ul li {
    font-size: 15px !important;
    margin-bottom: 10px !important;
  }

  .heading-section h2 {
    font-size: 28px;
  }

  /* Fix for ONS logo section on mobile */
  .ftco-section.ftco-no-pb.ftco-no-pt .img.w-100[style*="ONS_logo.jpg"] {
    min-height: 250px !important;
    height: 250px !important;
    background-position: center !important;
    margin-bottom: 20px;
  }

  /* Add more spacing between sections on mobile */
  .ftco-section.ftco-no-pb.ftco-no-pt {
    padding-top: 40px !important;
    margin-top: 30px !important;
  }

  /* Ensure proper spacing after team section */
  #about-team + .ftco-section.ftco-no-pb.ftco-no-pt {
    margin-top: 50px !important;
    padding-top: 50px !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    color: #000;
    background: #fff;
  }

  a {
    color: #000;
    text-decoration: underline;
  }
}

/* Performance optimizations */
img, video {
  max-width: 100%;
  height: auto;
}

/* Lazy loading for images */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
  opacity: 1;
}

/* Optimize large background images */
.img.w-100[style*="ONS_logo.jpg"] {
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  min-height: 400px;
  /* Add compression hint for browsers */
  image-rendering: optimizeQuality;
}

/* Preload critical images */
.hero-wrap {
  /* Ensure hero background loads quickly */
  background-attachment: scroll; /* Better performance than fixed */
}

/* Reduce animation complexity on mobile for better performance */
@media (max-width: 768px) {
  .ftco-animate {
    animation-duration: 0.3s !important; /* Faster animations on mobile */
  }

  /* Disable parallax on mobile for better performance */
  .hero-wrap[data-stellar-background-ratio] {
    background-attachment: scroll !important;
  }
}

/* Team member photos styling for consistency */
.agent img.img-fluid {
  object-fit: cover;
  height: 300px; /* Fixed height to maintain consistency across team photos */
  width: 100%; /* Ensure the image takes full width of container */
}

/* Custom styling for Vansh's photo to improve focus */
.agent img[alt="Vansh Agarwal"],
.vansh-photo {
  object-position: center 30%; /* Adjust vertical position to focus on face */
}

/* Testimonial styling - Brand colors */
.testimony-wrap .name {
  color: #F9B949 !important; /* Brand yellow for client names */
}

.testimony-wrap .fa-quote-left {
  color: #F9B949 !important; /* Brand yellow for quote icons */
}

/* Counter section - Brand yellow numbers */
.ftco-counter .number {
  color: #F9B949 !important; /* Brand yellow for counter numbers */
}

/* Additional performance optimizations */
/* Optimize font loading */
@font-face {
  font-family: 'Nunito Sans';
  font-display: swap; /* Improve font loading performance */
}

/* Optimize animations for better performance */
.ftco-animate {
  will-change: transform, opacity;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .ftco-animate,
  .owl-carousel,
  .carousel-testimony {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize background images for better loading */
.hero-wrap {
  background-attachment: scroll !important; /* Better performance than fixed */
  will-change: transform;
}

/* Optimize team member images */
.agent .img,
.list-team .img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  will-change: transform;
}

/* Optimize project images */
.property-wrap .img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s ease;
}

/* Critical CSS for above-the-fold content */
.navbar {
  will-change: transform;
}

/* Optimize loader */
#ftco-loader {
  will-change: opacity;
}



/* Improve form styling */
.form-control:focus {
  border-color: #3e64ff;
  box-shadow: 0 0 0 0.2rem rgba(62, 100, 255, 0.25);
}

/* Improve button accessibility */
.btn {
  position: relative;
  overflow: hidden;
}

.btn:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn:focus:after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}
