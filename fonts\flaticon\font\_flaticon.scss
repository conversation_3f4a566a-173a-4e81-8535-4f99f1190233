    /*
    Flaticon icon font: Flaticon
    Creation date: 04/06/2020 04:07
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-business:before { content: "\f100"; }
.flaticon-home:before { content: "\f101"; }
.flaticon-stats:before { content: "\f102"; }
.flaticon-quarantine:before { content: "\f103"; }
.flaticon-home-repair:before { content: "\f104"; }
.flaticon-sales:before { content: "\f105"; }
.flaticon-bank:before { content: "\f106"; }
.flaticon-team:before { content: "\f107"; }
    
    $font-Flaticon-business: "\f100";
    $font-Flaticon-home: "\f101";
    $font-Flaticon-stats: "\f102";
    $font-Flaticon-quarantine: "\f103";
    $font-Flaticon-home-repair: "\f104";
    $font-Flaticon-sales: "\f105";
    $font-Flaticon-bank: "\f106";
    $font-Flaticon-team: "\f107";