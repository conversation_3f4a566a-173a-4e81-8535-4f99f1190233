<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Contact | Open Network Solutions</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="https://fonts.googleapis.com/css?family=Nunito+Sans:200,300,400,600,700,800,900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <link rel="stylesheet" href="css/animate.css">

    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/magnific-popup.css">

    <link rel="stylesheet" href="css/flaticon.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/custom.css">
  </head>
  <body>

	  <nav class="navbar navbar-expand-lg navbar-dark ftco_navbar bg-dark ftco-navbar-light" id="ftco-navbar">
	    <div class="container">
	      <a class="navbar-brand" href="index.html"><img src="images/ONSLOGO.png" alt="Open Network Solutions" style="height: 50px;"></a>
	      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#ftco-nav" aria-controls="ftco-nav" aria-expanded="false" aria-label="Toggle navigation">
	        <span class="oi oi-menu"></span> Menu
	      </button>

	      <div class="collapse navbar-collapse" id="ftco-nav">
	        <ul class="navbar-nav ml-auto">
	          <li class="nav-item"><a href="index.html" class="nav-link">Home</a></li>
	          <li class="nav-item"><a href="about.html" class="nav-link">About</a></li>
	          <li class="nav-item"><a href="services.html" class="nav-link">Services</a></li>
	          <li class="nav-item"><a href="projects.html" class="nav-link">Projects</a></li>
	          <li class="nav-item active"><a href="contact.html" class="nav-link">Contact</a></li>
	        </ul>
	      </div>
	    </div>
	  </nav>
    <!-- END nav -->

    <section class="hero-wrap hero-wrap-2 contact-hero" style="min-height: 650px; background-image: url('images/Main_Bg.jpg') !important; background-size: cover !important; background-position: center center !important;" data-stellar-background-ratio="0.5">
      <div class="container">
        <div class="row no-gutters slider-text js-fullheight align-items-center justify-content-center">
          <div class="col-md-12 ftco-animate pb-0 text-center">
            <div class="hero-content-wrapper">
              <h1 class="contact-hero-title">CONTACT US</h1>
              <div class="down-arrow-container">
                <a href="#contact-section" class="smooth-scroll">
                  <span class="fa fa-chevron-down down-arrow-icon"></span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="contact-section" class="ftco-section contact-section">
      <div class="container">
        <div class="row justify-content-center mb-5">
          <div class="col-md-10 text-center">
            <h2 class="mb-4">Get In Touch With Us</h2>
            <p class="mb-5">Have questions about our services or want to discuss a project? We're here to help!</p>
          </div>
        </div>

        <!-- Contact Information Cards -->
        <div class="row d-flex mb-5 contact-info justify-content-center">
          <div class="col-md-10">
            <div class="row">
              <!-- Address Section -->
              <div class="col-md-4 mb-4">
                <div class="contact-info-box text-center py-4 px-3 bg-light rounded">
                  <div class="icon d-flex align-items-center justify-content-center rounded-circle mb-3">
                    <span class="fa fa-map"></span>
                  </div>
                  <h3 class="mb-3">Address</h3>
                  <p>B-56, Sector 22 <br> Noida, Uttar Pradesh</p>
                </div>
              </div>

              <!-- Phone Section -->
              <div class="col-md-4 mb-4">
                <div class="contact-info-box text-center py-4 px-3 bg-light rounded">
                  <div class="icon d-flex align-items-center justify-content-center rounded-circle mb-3">
                    <span class="fa fa-phone"></span>
                  </div>
                  <h3 class="mb-3">Book a Call</h3>
                  <a href="https://lunacal.ai/chaitanya-yadav-ons/10min" target="_blank" class="btn btn-primary">Schedule Now</a>
                </div>
              </div>

              <!-- Email Section -->
              <div class="col-md-4 mb-4">
                <div class="contact-info-box text-center py-4 px-3 bg-light rounded">
                  <div class="icon d-flex align-items-center justify-content-center rounded-circle mb-3">
                    <span class="fa fa-paper-plane"></span>
                  </div>
                  <h3 class="mb-3">Email</h3>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Form Section -->
        <div class="row block-9 justify-content-center">
          <div class="col-md-8 mb-5">
            <h2 class="text-center mb-4">Send Us a Message</h2>
            <p class="text-center mb-5">Fill out the form below and we will get back to you as soon as possible!</p>
            <div class="bg-light p-5 rounded">
              <iframe data-tally-src="https://tally.so/embed/3X1vAz?alignLeft=1&hideTitle=1&transparentBackground=1&dynamicHeight=1" loading="lazy" width="100%" height="400" frameborder="0" marginheight="0" marginwidth="0" title="Contact form Production"></iframe>
              <script>var d=document,w="https://tally.so/widgets/embed.js",v=function(){"undefined"!=typeof Tally?Tally.loadEmbeds():d.querySelectorAll("iframe[data-tally-src]:not([src])").forEach((function(e){e.src=e.dataset.tallySrc}))};if("undefined"!=typeof Tally)v();else if(d.querySelector('script[src="'+w+'"]')==null){var s=d.createElement("script");s.src=w,s.onload=v,s.onerror=v,d.body.appendChild(s);}</script>
            </div>
          </div>
        </div>
      </div>
    </section>

    <footer class="ftco-footer ftco-section">
      <div class="container">
        <div class="row mb-5">
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Open Network Solutions</h2>
              <p>Open Network Solutions delivers scalable IT consulting, strategic partnerships, and end-to-end project management to help businesses innovate and grow.</p>
              <ul class="ftco-footer-social list-unstyled mt-5">
                <li class="ftco-animate"><a href="https://www.linkedin.com/company/open-network-solutions-india/" target="_blank"><span class="fa fa-linkedin"></span></a></li>
                <li class="ftco-animate"><a href="https://www.facebook.com/profile.php?id=61566873158571" target="_blank"><span class="fa fa-facebook"></span></a></li>
                <li class="ftco-animate"><a href="https://www.instagram.com/opennetworksolutions/" target="_blank"><span class="fa fa-instagram"></span></a></li>
              </ul>
            </div>
          </div>
          <!-- <div class="col-md">
            <div class="ftco-footer-widget mb-4 ml-md-4">
              <h2 class="ftco-heading-2">Community</h2>
              <ul class="list-unstyled">
                <li><a href="#"><span class="fa fa-chevron-right mr-2"></span>Search Properties</a></li>
                <li><a href="#"><span class="fa fa-chevron-right mr-2"></span>For Agents</a></li>
                <li><a href="#"><span class="fa fa-chevron-right mr-2"></span>Reviews</a></li>
                <li><a href="#"><span class="fa fa-chevron-right mr-2"></span>FAQs</a></li>
              </ul>
            </div>
          </div> -->
          <div class="col-md">
            <div class="ftco-footer-widget mb-4 ml-md-4">
              <h2 class="ftco-heading-2">About Us</h2>
              <ul class="list-unstyled">
                <li><a href="about.html"><span class="fa fa-chevron-right mr-2"></span>Our Story</a></li>
                <li><a href="about.html#team"><span class="fa fa-chevron-right mr-2"></span>Meet the team</a></li>
                <li><a href="services.html"><span class="fa fa-chevron-right mr-2"></span>Services</a></li>
              </ul>
            </div>
          </div>
          <div class="col-md">
             <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Company</h2>
              <ul class="list-unstyled">
                <li><a href="about.html"><span class="fa fa-chevron-right mr-2"></span>About Us</a></li>
                <li><a href="contact.html"><span class="fa fa-chevron-right mr-2"></span>Contact Us</a></li>
                <li><a href="https://www.linkedin.com/company/open-network-solutions-india/jobs" target="_blank"><span class="fa fa-chevron-right mr-2"></span>Careers</a></li>
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
            	<h2 class="ftco-heading-2">Have Questions?</h2>
            	<div class="block-23 mb-3">
	              <ul>
	                <li><span class="icon fa fa-map"></span><span class="text">B-56, Sector 22 <br> Noida, Uttar Pradesh</span></li>
	                <li><a href="https://lunacal.ai/chaitanya-yadav-ons/10min" target="_break"><span class="icon fa fa-phone"></span><span class="text">Book a Free Call</span></a></li>
	                <li><a href="mailto:<EMAIL>"><span class="icon fa fa-envelope pr-4"></span><span class="text">Click Here</span></a></li>
	              </ul>
	            </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 text-center">

            <p class="footer-company-name">Open Network Solutions India! All Rights Reserved </p>
          </div>
        </div>
      </div>
    </footer>



  <!-- loader -->
  <div id="ftco-loader" class="show fullscreen"><svg class="circular" width="48px" height="48px"><circle class="path-bg" cx="24" cy="24" r="22" fill="none" stroke-width="4" stroke="#eeeeee"/><circle class="path" cx="24" cy="24" r="22" fill="none" stroke-width="4" stroke-miterlimit="10" stroke="#F96D00"/></svg></div>


  <script src="js/jquery.min.js"></script>
  <script src="js/jquery-migrate-3.0.1.min.js"></script>
  <script src="js/popper.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/jquery.easing.1.3.js"></script>
  <script src="js/jquery.waypoints.min.js"></script>
  <script src="js/jquery.stellar.min.js"></script>
  <script src="js/owl.carousel.min.js"></script>
  <script src="js/jquery.magnific-popup.min.js"></script>
  <script src="js/jquery.animateNumber.min.js"></script>
  <script src="js/scrollax.min.js"></script>

  <script src="js/main.js"></script>

  <script>
    // Smooth scroll function for the down arrow
    document.addEventListener('DOMContentLoaded', function() {
      const smoothScrollLinks = document.querySelectorAll('.smooth-scroll');

      smoothScrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();

          const targetId = this.getAttribute('href');
          const targetElement = document.querySelector(targetId);

          if (targetElement) {
            window.scrollTo({
              top: targetElement.offsetTop - 80, // Offset for fixed header
              behavior: 'smooth'
            });
          }
        });
      });
    });
  </script>

  </body>
</html>