{"hosting": {"public": ".", "ignore": ["firebase.json", ".firebaserc", ".git/**", ".github/**", "**/.*", "**/node_modules/**", "README.md", "LICENSE", "unused_assets/**", "fonts/flaticon/license/**", "fonts/flaticon/font/flaticon.html", "fonts/flaticon/font/_flaticon.scss", "js/jquery-3.2.1.min.js", "*.md", "*.log", "*.txt", "*.json.backup", "*.bak", "*.tmp"], "headers": [{"source": "**/*.@(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(css|js)", "headers": [{"key": "Cache-Control", "value": "public, max-age=604800"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "**/*.@(eot|ttf|woff|woff2)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "cleanUrls": true, "trailingSlash": false, "redirects": [{"source": "/home", "destination": "/", "type": 301}]}}